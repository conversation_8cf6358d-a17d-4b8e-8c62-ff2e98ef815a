import threading
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import get_user_model
import jwt
from django.conf import settings
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
import json
from django.http import JsonR<PERSON>ponse
from django.utils.deprecation import MiddlewareMixin

# Thread local storage
_thread_locals = threading.local()


def get_current_user():
    """
    Returns the current user from thread local storage
    """
    return getattr(_thread_locals, 'user', None)


def get_current_user_id():
    """
    Returns the current user ID from thread local storage
    """
    user = get_current_user()
    if user and hasattr(user, 'id'):
        return user.id
    return None


class CurrentUserMiddleware(MiddlewareMixin):
    """
    Middleware that stores the current authenticated user in thread local storage
    """

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.get_response = get_response
        self.jwt_auth = JWTAuthentication()
        self.User = get_user_model()

    def extract_user_from_jwt(self, request):
        """
        Extract user from JWT token in the request headers
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth_header.startswith('Bearer '):
            return None
            
        token = auth_header.split(' ')[1]
        try:
            validated_token = self.jwt_auth.get_validated_token(token)
            user_id = validated_token.get('user_id')
            if not user_id:
                return None
                
            try:
                return self.User.objects.get(id=user_id)
            except self.User.DoesNotExist:
                return None
                
        except (InvalidToken, TokenError):
            return None

    def process_request(self, request):
        if hasattr(_thread_locals, 'user'):
            delattr(_thread_locals, 'user')
            
        if hasattr(request, 'user') and request.user.is_authenticated:
            _thread_locals.user = request.user
            return
            
        user = self.extract_user_from_jwt(request)
        if user:
            _thread_locals.user = user
            request.user = user
        else:
            _thread_locals.user = None
            
    def process_response(self, request, response):
        return response


class APIVersionMiddleware(MiddlewareMixin):
    """
    Middleware that adds API_VERSION to all JSON responses
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.get_response = get_response
        
    def process_response(self, request, response):
        # Check if the response is a JSON response
        if hasattr(response, 'content_type') and 'application/json' in response.get('Content-Type', ''):
            try:
                # Decode the response content
                content = response.content.decode('utf-8')
                data = json.loads(content)
                
                # Add API_VERSION to the response data
                if isinstance(data, dict):
                    data['version'] = settings.API_VERSION
                    
                    # Encode the response content back
                    response.content = json.dumps(data).encode('utf-8')
                    
                    # Update Content-Length
                    response['Content-Length'] = len(response.content)
            except json.JSONDecodeError:
                # Not a valid JSON response, skip
                pass
            except Exception as e:
                # Log the error but don't block the response
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error adding API_VERSION to response: {str(e)}")
        
        return response


class PowerBIAPIKeyMiddleware(MiddlewareMixin):
    """
    Middleware that validates API key authentication for PowerBI endpoints.
    Only applies to requests with paths starting with 'api/powerbi/'.
    """

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.get_response = get_response
        self.powerbi_path_prefix = 'api/powerbi/'

    def process_request(self, request):
        """
        Process the request and validate API key for PowerBI endpoints.
        """
        # Check if this is a PowerBI API request
        if not self._is_powerbi_request(request):
            return None

        # Validate API key for PowerBI requests
        api_key = self._get_api_key_from_request(request)

        if not api_key:
            return self._create_error_response(
                "API key is required for PowerBI endpoints",
                "missing_api_key"
            )

        if not self._is_valid_api_key(api_key):
            return self._create_error_response(
                "Invalid API key",
                "invalid_api_key"
            )

        # API key is valid, allow the request to proceed
        return None

    def _is_powerbi_request(self, request):
        """
        Check if the request is for a PowerBI endpoint.
        """
        path = request.path_info.lstrip('/')
        return path.startswith(self.powerbi_path_prefix)

    def _get_api_key_from_request(self, request):
        """
        Extract the API key from the x-api-key header.
        """
        return request.META.get('HTTP_X_API_KEY')

    def _is_valid_api_key(self, api_key):
        """
        Validate the provided API key against the configured key.
        """
        expected_key = getattr(settings, 'POWERBI_API_KEY', None)
        if not expected_key:
            # Fallback to DRT_API_KEY if POWERBI_API_KEY is not set
            expected_key = getattr(settings, 'DRT_API_KEY', None)

        return api_key and expected_key and api_key == expected_key

    def _create_error_response(self, message, error_code):
        """
        Create a standardized error response for authentication failures.
        """
        return JsonResponse({
            'status': 'error',
            'code': error_code,
            'message': message,
            'version': getattr(settings, 'API_VERSION', 'v1.0.0')
        }, status=401)
