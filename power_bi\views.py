from drf_spectacular.utils import extend_schema
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from course.models import Course
from .serializers import PowerBICourseSerializer    


@extend_schema(
    tags=["Power BI"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_course(request):
    try:
        courses = Course.objects.select_related('masCourseType').order_by('-id')
        total = courses.count()

        return Response({
            "data": PowerBICourseSerializer(courses, many=True).data,
            "total": total,
        }, status=200)
    except Exception as e:
        return Response({
            "error": "Error",
            "message": str(e)
        }, status=500)
